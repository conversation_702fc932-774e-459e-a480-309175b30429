import React, { useCallback, useState, useEffect } from "react";
import { Box, IconButton, Tooltip, Typography, Divider } from "@mui/material";
import LandscapeIcon from "@mui/icons-material/Landscape";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import FormatColorResetIcon from "@mui/icons-material/FormatColorReset";
import { StoreContext } from "../../store";
import { CustomPopover } from ".";

export const BACKGROUND_COLORS = [
  "#FFFFFF", // White
  "#000000", // Black
  "#F5F5F5", // Light Gray
  "#333333", // Dark Gray
  "#FF5252", // Red
  "#4CAF50", // Green
  "#2196F3", // Blue
  "#FFC107", // Yellow
  "#9C27B0", // Purple
  "#FF9800", // Orange
];

// Brand colors
export const BRAND_COLORS = [
  "#4DD0E1", // Teal/Cyan
  "#7B1FA2", // Purple
];

// Recommended colors
export const RECOMMENDED_COLORS = [
  "#FFFFFF", // White
  "#FFCDD2", // Light Pink
  "#F8BBD0", // Pink
  "#FFF9C4", // Light Yellow
  "#DCEDC8", // Light Green
  "#BBDEFB", // Light Blue
  "#E1BEE7", // Light Purple

  "#9E9E9E", // Gray
  "#E57373", // Red
  "#F06292", // Pink
  "#FFA726", // Orange
  "#66BB6A", // Green
  "#42A5F5", // Blue
  "#7E57C2", // Purple

  "#212121", // Black
  "#C62828", // Dark Red
  "#AD1457", // Dark Pink
  "#8D6E63", // Brown
  "#2E7D32", // Dark Green
  "#1565C0", // Dark Blue
  "#6A1B9A", // Dark Purple
];

const MAX_RECENT_COLORS = 8;

export const BackgroundSelector = React.memo(() => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const store = React.useContext(StoreContext);
  const [recentColors, setRecentColors] = useState<string[]>([]);

  // Load recent colors from localStorage on component mount
  useEffect(() => {
    const storedRecentColors = localStorage.getItem("recentBackgroundColors");
    if (storedRecentColors) {
      setRecentColors(JSON.parse(storedRecentColors));
    }
  }, []);

  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    },
    []
  );

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const open = Boolean(anchorEl);

  const updateRecentColors = useCallback((color: string) => {
    setRecentColors((prevColors) => {
      // Remove the color if it already exists
      const filteredColors = prevColors.filter((c) => c !== color);
      // Add the color to the beginning
      const newColors = [color, ...filteredColors].slice(0, MAX_RECENT_COLORS);
      // Save to localStorage
      localStorage.setItem("recentBackgroundColors", JSON.stringify(newColors));
      return newColors;
    });
  }, []);

  const handleColorSelect = useCallback(
    (color: string) => {
      store.setBackgroundColor(color);
      store.saveChange();
      updateRecentColors(color);
      handleClose();
    },
    [store, handleClose, updateRecentColors]
  );

  const renderColorBox = useCallback(
    (color: string, index: number) => (
      <Box
        key={index}
        onClick={() => handleColorSelect(color)}
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          cursor: "pointer",
          background: color,
          border: "1px solid #e0e0e0",
          transition: "transform 0.2s",
          "&:hover": {
            transform: "scale(1.05)",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          },
        }}
      />
    ),
    [handleColorSelect]
  );

  return (
    <>
      <Tooltip title="Background" arrow>
        <IconButton onClick={handleClick}>
          <LandscapeIcon sx={{ fontSize: 22 }} />
        </IconButton>
      </Tooltip>
      <CustomPopover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        width={300}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Recents
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 3 }}>
            <Box
              onClick={() => handleColorSelect("transparent")}
              sx={{
                width: 40,
                height: 40,
                borderRadius: 1,
                cursor: "pointer",
                border: "1px solid #e0e0e0",
                position: "relative",
                "&:hover": {
                  transform: "scale(1.05)",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                },
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: "white",
                },
                "&::after": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background:
                    "linear-gradient(to bottom right, transparent calc(50% - 1px), red, transparent calc(50% + 1px))",
                },
              }}
            />
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 1,
                cursor: "pointer",
                border: "1px solid #e0e0e0",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                "&:hover": {
                  transform: "scale(1.05)",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                },
              }}
            >
              <FormatColorResetIcon fontSize="small" />
            </Box>
            {recentColors.map(renderColorBox)}
          </Box>

          <Typography variant="body2" sx={{ mb: 2 }}>
            Recommended
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
            {RECOMMENDED_COLORS.map(renderColorBox)}
          </Box>
        </Box>
      </CustomPopover>
    </>
  );
});

BackgroundSelector.displayName = "BackgroundSelector";
