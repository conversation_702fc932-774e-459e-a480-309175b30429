import React from "react";
import { Box, Popover, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

// Common styles
const COMMON_STYLES = {
  popoverBox: {
    p: 2,
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    top: 2,
    right: 2,
  },
};

// Common Popover component
export interface CustomPopoverProps {
  open: boolean;
  anchorEl: HTMLElement | null;
  onClose: () => void;
  children: React.ReactNode;
  width?: number;
  anchorOrigin?: {
    vertical: "top" | "center" | "bottom";
    horizontal: "left" | "center" | "right";
  };
  transformOrigin?: {
    vertical: "top" | "center" | "bottom";
    horizontal: "left" | "center" | "right";
  };
  sx?: React.ComponentProps<typeof Popover>["sx"];
}

export const CustomPopover = React.memo(
  ({
    open,
    anchorEl,
    onClose,
    children,
    width = 240,
    anchorOrigin = {
      vertical: "bottom" as const,
      horizontal: "left" as const,
    },
    transformOrigin = {
      vertical: "top" as const,
      horizontal: "left" as const,
    },
    sx,
  }: CustomPopoverProps) => (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
      sx={sx}
    >
      <Box sx={{ ...COMMON_STYLES.popoverBox, width }}>
        <IconButton
          size="small"
          onClick={onClose}
          sx={COMMON_STYLES.closeButton}
          aria-label="close"
        >
          <CloseIcon fontSize="small" />
        </IconButton>
        {children}
      </Box>
    </Popover>
  )
);

CustomPopover.displayName = "CustomPopover";
