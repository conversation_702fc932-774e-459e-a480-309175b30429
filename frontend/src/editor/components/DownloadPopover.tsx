import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  IconButton,
  LinearProgress,
  Tooltip,
  Typography,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { StoreContext } from "../../store";
import { CustomPopover } from ".";

// Constants
const API_BASE_URL = "http://localhost:8080/api";
const PROGRESS_CHECK_INTERVAL = 1000; // 1 second

// Common styles
const COMMON_STYLES = {
  flexColumnWithGap: {
    display: "flex",
    flexDirection: "column",
    gap: 2,
  },
};

// Define a function to display friendly stage names
const getStageDisplayName = (stage: string): string => {
  const stageMap: Record<string, string> = {
    initializing: "Initializing",
    detecting_audio: "Detecting Audio",
    detecting_audio_elements: "Analyzing Audio Elements",
    processing_elements: "Processing Elements",
    processing_audio_elements: "Processing Audio",
    processing_visual_elements: "Processing Visual Elements",
    processing_captions: "Processing Captions",
    building_command: "Building Command",
    generating_command: "Generating Command",
    rendering: "Rendering",
    finalizing: "Finalizing",
    completed: "Completed",
    failed: "Failed",
    cancelled: "Cancelled",
  };

  return stageMap[stage] || stage;
};

interface IDownloadState {
  renderId: string;
  progress: number;
  isDownloading: boolean;
  status?: "pending" | "processing" | "completed" | "failed" | "cancelled";
  stage?: string;
  error?: string;
  downloadUrl?: string;
}

const INITIAL_DOWNLOAD_STATE: IDownloadState = {
  progress: 0,
  isDownloading: false,
  renderId: "",
  status: undefined,
  error: undefined,
  downloadUrl: undefined,
};

export const DownloadPopover = React.memo(() => {
  const store = React.useContext(StoreContext);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [downloadState, setDownloadState] = useState<IDownloadState>(
    INITIAL_DOWNLOAD_STATE
  );
  const [exportFormat, setExportFormat] = useState("mp4");
  const [formatMenuAnchorEl, setFormatMenuAnchorEl] =
    useState<HTMLButtonElement | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const intervalRef = React.useRef<NodeJS.Timeout | null>(null);

  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    },
    []
  );

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleFormatMenuOpen = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      setFormatMenuAnchorEl(event.currentTarget);
    },
    []
  );

  const handleFormatMenuClose = useCallback(() => {
    setFormatMenuAnchorEl(null);
  }, []);

  const handleFormatSelect = useCallback(
    (format: string) => {
      setExportFormat(format);
      handleFormatMenuClose();
    },
    [handleFormatMenuClose]
  );

  const open = Boolean(anchorEl);
  const formatMenuOpen = Boolean(formatMenuAnchorEl);

  const resetDownloadState = useCallback(() => {
    setDownloadState(INITIAL_DOWNLOAD_STATE);
  }, []);

  const handleExport = useCallback(async () => {
    try {
      setIsExporting(true);
      setDownloadState((prev) => ({
        ...prev,
        isDownloading: true,
        progress: 0,
      }));
      console.log("downloading");
      const data = await store.exportVideo(exportFormat);

      setDownloadState((prev) => ({
        ...prev,
        renderId: data.taskId,
      }));
    } catch (error) {
      console.error("Export failed:", error);
      resetDownloadState();
      // Add error notification here
      alert("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
    }
  }, [exportFormat, resetDownloadState, store]);

  useEffect(() => {
    if (downloadState.renderId && downloadState.isDownloading) {
      intervalRef.current = setInterval(async () => {
        try {
          const progressUrl = `${API_BASE_URL}/progress/${downloadState.renderId}`;
          console.log("Fetching progress from:", progressUrl);

          const response = await fetch(progressUrl);

          if (!response.ok) {
            throw new Error(`Failed to fetch progress: ${response.status}`);
          }

          const data = await response.json();
          console.log("Progress data:", data);

          if (data.status === "completed") {
            // 生成下载URL
            const downloadUrl = `${API_BASE_URL}/download/${downloadState.renderId}`;
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              progress: 100,
              stage: data.stage || "completed",
              downloadUrl,
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.status === "failed") {
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              stage: data.stage || "failed",
              error: data.error || "Video generation failed",
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.status === "cancelled") {
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              stage: data.stage || "cancelled",
              error: data.error || "Task cancelled",
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.progress !== undefined) {
            // 区分处理中状态和最终处理状态
            setDownloadState((prev) => {
              // 只有当新进度大于当前进度时才更新
              if (data.progress > prev.progress || data.stage !== prev.stage) {
                return {
                  ...prev,
                  status: data.status,
                  progress: data.progress,
                  stage: data.stage || prev.stage,
                };
              }
              return prev;
            });

            // 如果进度已达到99%且阶段是finalizing，增加检查频率
            if (data.progress >= 99 && data.stage === "finalizing") {
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
              }
              // 每200ms检查一次，加快响应速度
              intervalRef.current = setInterval(async () => {
                try {
                  const fastCheckResponse = await fetch(progressUrl);
                  const fastCheckData = await fastCheckResponse.json();

                  if (fastCheckData.status === "completed") {
                    const downloadUrl = `${API_BASE_URL}/download/${downloadState.renderId}`;
                    setDownloadState((prev) => ({
                      ...prev,
                      status: fastCheckData.status,
                      progress: 100,
                      stage: fastCheckData.stage || "completed",
                      downloadUrl,
                    }));
                    if (intervalRef.current) {
                      clearInterval(intervalRef.current);
                      intervalRef.current = null;
                    }
                  }
                } catch (error) {
                  console.error("Error in fast progress check:", error);
                }
              }, 200);
            }
          }
        } catch (error) {
          console.error("Error fetching progress:", error);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          resetDownloadState();
        }
      }, PROGRESS_CHECK_INTERVAL);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [
    downloadState.renderId,
    downloadState.isDownloading,
    handleClose,
    resetDownloadState,
  ]);

  const formatOptions = useMemo(
    () => [
      { value: "mp4", label: "MP4" },
      { value: "mov", label: "MOV" },
      { value: "webm", label: "WebM" },
      { value: "gif", label: "GIF" },
    ],
    []
  );

  const handleCopyLink = useCallback(() => {
    navigator.clipboard.writeText("Your video link here");
    // Add a notification that link was copied
  }, []);

  const currentFormatLabel = useMemo(
    () =>
      formatOptions.find((opt) => opt.value === exportFormat)?.label || "MP4",
    [formatOptions, exportFormat]
  );

  // Define a function to cancel video processing on the server
  const cancelVideoProcessing = useCallback(
    async (taskId: string) => {
      try {
        if (!taskId) return;

        const response = await fetch(`${API_BASE_URL}/cancel/${taskId}`, {
          method: "POST",
        });

        if (!response.ok) {
          throw new Error(`Failed to cancel task: ${response.status}`);
        }

        console.log("Task cancelled successfully");
      } catch (error) {
        console.error("Error cancelling task:", error);
      } finally {
        // Clean up frontend resources regardless of server response
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        resetDownloadState();
      }
    },
    [resetDownloadState]
  );

  // 处理下载视频后的操作
  const handleDownloadVideo = useCallback(() => {
    // 关闭对话窗口
    handleClose();
    // 恢复初始状态
    resetDownloadState();
  }, [handleClose, resetDownloadState]);

  return (
    <>
      <Tooltip title="Export" arrow>
        <IconButton onClick={handleClick} disabled={isExporting}>
          <DownloadIcon sx={{ fontSize: 20 }} />
        </IconButton>
      </Tooltip>
      <CustomPopover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        {downloadState.isDownloading ? (
          <Box sx={COMMON_STYLES.flexColumnWithGap}>
            <Typography variant="body2">
              {downloadState.status === "completed"
                ? "Generation Complete"
                : downloadState.status === "failed"
                ? "Generation Failed"
                : downloadState.status === "cancelled"
                ? "Cancelled"
                : downloadState.stage
                ? `Generating video... (${getStageDisplayName(
                    downloadState.stage
                  )})`
                : "Generating video..."}
            </Typography>
            {downloadState.error && (
              <Typography variant="caption" color="error">
                {downloadState.error}
              </Typography>
            )}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <LinearProgress
                variant="determinate"
                value={downloadState.progress}
                sx={{
                  flexGrow: 1,
                  height: 8,
                  borderRadius: 0.5,
                  backgroundColor:
                    downloadState.status === "failed"
                      ? "error.light"
                      : undefined,
                }}
              />
              <Box
                sx={{
                  border: 1,
                  borderColor: "divider",
                  p: 0.5,
                  borderRadius: 0.5,
                  minWidth: 40,
                  textAlign: "center",
                }}
              >
                <Typography variant="caption" color="text.secondary">
                  {Math.round(downloadState.progress)}%
                </Typography>
              </Box>
            </Box>
            {downloadState.status === "completed" ? (
              <Button
                variant="contained"
                size="small"
                fullWidth
                href={downloadState.downloadUrl}
                download
                onClick={handleDownloadVideo}
              >
                Download Video
              </Button>
            ) : downloadState.status === "failed" ? (
              <Button
                variant="contained"
                size="small"
                fullWidth
                onClick={handleExport}
                color="error"
              >
                Retry
              </Button>
            ) : downloadState.status === "cancelled" ? (
              <Button
                variant="contained"
                size="small"
                fullWidth
                onClick={handleExport}
              >
                Restart
              </Button>
            ) : (
              <Button
                variant="outlined"
                size="small"
                fullWidth
                onClick={() => {
                  cancelVideoProcessing(downloadState.renderId);
                }}
              >
                Cancel
              </Button>
            )}
          </Box>
        ) : (
          <Box sx={COMMON_STYLES.flexColumnWithGap}>
            <Typography variant="body2" fontWeight="medium">
              Export settings
            </Typography>
            <Button
              variant="outlined"
              fullWidth
              endIcon={<KeyboardArrowDownIcon />}
              sx={{
                justifyContent: "space-between",
                textTransform: "none",
              }}
              onClick={handleFormatMenuOpen}
            >
              {currentFormatLabel}
            </Button>
            <CustomPopover
              open={formatMenuOpen}
              anchorEl={formatMenuAnchorEl}
              onClose={handleFormatMenuClose}
              width={120}
            >
              <Box sx={{ py: 1 }}>
                {formatOptions.map((option) => (
                  <Box
                    key={option.value}
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: "pointer",
                      "&:hover": { bgcolor: "action.hover" },
                      bgcolor:
                        exportFormat === option.value
                          ? "action.selected"
                          : "transparent",
                    }}
                    onClick={() => handleFormatSelect(option.value)}
                  >
                    <Typography variant="body2">{option.label}</Typography>
                  </Box>
                ))}
              </Box>
            </CustomPopover>
            <Button
              variant="contained"
              size="small"
              fullWidth
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? "Preparing export..." : "Export"}
            </Button>
          </Box>
        )}
      </CustomPopover>
    </>
  );
});

DownloadPopover.displayName = "DownloadPopover";
