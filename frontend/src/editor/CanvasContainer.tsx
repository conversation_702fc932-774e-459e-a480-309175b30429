import { observer } from "mobx-react-lite";
import { Box, Paper, useTheme } from "@mui/material";
import { fabric } from "fabric";
import React, { useEffect, useRef, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { MapInteractionCSS } from "react-map-interaction";
import { StoreContext } from "../store";
import initControl, {
  handleMouseOutCorner,
  handleMouseOverCorner,
} from "./components/controller";
import { initAligningGuidelines, initCenteringGuidelines } from "./guide-lines";

export const CanvasContainer = observer(() => {
  const store = React.useContext(StoreContext);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const boxRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  // 合并相关状态
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(
    null
  );
  const [hasSelectedElement, setHasSelectedElement] = useState(false);

  // 使用Store中的缩放值，提取初始化逻辑为单独函数
  const initializeCanvasScale = () => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const scaleX = (windowWidth * 0.7) / 1920;
    const scaleY = (windowHeight * 0.7) / 1080;
    const scale = Math.min(scaleX, scaleY);
    const x = windowWidth / 2;
    const y = (windowHeight - 260) / 2;

    store.updateCanvasScale(scale, { x, y });
    return {
      scale: store.canvasScale,
      translation: store.canvasTranslation,
    };
  };

  const [value, setValue] = useState(initializeCanvasScale);

  // 合并快捷键处理逻辑
  useHotkeys(["ctrl+z", "meta+z"], () => {
    const actionType = store.getUndoActionType();
    if (actionType) {
      store.undo();
    }
  });

  useHotkeys(["ctrl+y", "meta+shift+z"], () => {
    const actionType = store.getRedoActionType();
    if (actionType) {
      store.redo();
    }
  });

  // useHotkeys(["ctrl+=", "ctrl+plus", "meta+=", "meta+plus"], () => {
  //   setValue(store.zoomIn());
  // });

  // useHotkeys(["ctrl+-", "ctrl+minus", "meta+-", "meta+minus"], () => {
  //   setValue(store.zoomOut());
  // });

  // useHotkeys(["ctrl+0", "meta+0"], () => {
  //   setValue(store.resetZoom());
  // });

  // 优化Canvas初始化
  useEffect(() => {
    if (store?.canvas) return;

    const canvas = new fabric.Canvas("myCanvas", {
      selection: true,
      preserveObjectStacking: true,
      selectionColor: "rgba(52, 152, 219,0.1)",
      selectionBorderColor: "rgba(52, 152, 219,1.0)",
      enableRetinaScaling: true,
      fireRightClick: false,
      controlsAboveOverlay: true,
      imageSmoothingEnabled: true,
      width: 1920,
      height: 1080,
    });

    // 设置Canvas属性
    const setCanvasProperties = () => {
      fabric.Object.prototype.cornerSize = 20;
      fabric.Object.prototype.borderColor = theme.palette.primary.main;
      fabric.Object.prototype.cornerColor = "white";
      fabric.Object.prototype.cornerStrokeColor = theme.palette.grey[400];
      fabric.Object.prototype.borderOpacityWhenMoving = 1;
      fabric.Object.prototype.centeredScaling = false;
      fabric.Object.prototype.centeredRotation = true;
      fabric.Object.prototype.transparentCorners = false;
    };

    setCanvasProperties();

    // @ts-ignore
    canvas.store = store;
    store.setCanvas(canvas);

    initControl();
    initAligningGuidelines(canvas);
    initCenteringGuidelines(canvas);

    // 初始化事件监听
    canvas.on("mouse:up", handleMouseUp);
    canvas.on("mouse:over", handleMouseOver);
    canvas.on("mouse:out", handleMouseOut);
    canvas.on("mouse:down", handleMouseDown);
    canvas.on("selection:created", handleSelection);
    canvas.on("selection:updated", handleSelection);
    canvas.on("selection:cleared", handleSelectionCleared);

    if (boxRef.current) {
      canvas.setWidth(boxRef.current.offsetWidth);
      canvas.setHeight(boxRef.current.offsetHeight);
    }

    canvas.requestRenderAll();
    store.loadFromLocalStorage();

    return () => {
      if (store.canvas) {
        canvas.off("mouse:up", handleMouseUp);
        canvas.off("mouse:over", handleMouseOver);
        canvas.off("mouse:out", handleMouseOut);
        canvas.off("mouse:down", handleMouseDown);
        canvas.off("selection:created", handleSelection);
        canvas.off("selection:updated", handleSelection);
        canvas.off("selection:cleared", handleSelectionCleared);
        store.destroy();
      }
    };
  }, [theme]);

  // 简化事件处理函数
  const handleSelection = (e: fabric.IEvent) => {
    const selected = e.selected?.[0];
    setSelectedObject(selected || null);
    setHasSelectedElement(!!selected);
  };

  const handleSelectionCleared = () => {
    setSelectedObject(null);
    setHasSelectedElement(false);
  };

  const handleMouseUp = () => {};

  const handleMouseOver = (e: fabric.IEvent) => {
    const corner = e.target ? (e.target as any).__corner : undefined;
    if (corner) {
      handleMouseOverCorner(corner, e.target);
    }
    store.canvas.renderAll();
  };

  const handleMouseOut = (e: fabric.IEvent) => {
    if (e.target) {
      e.target.set({ borderColor: null });
      handleMouseOutCorner(e.target);
      store.canvas.requestRenderAll();
    }
  };

  const handleMouseDown = (opt: fabric.IEvent) => {
    if (store.editMode === "move") {
      if (!opt.target) {
        store.setSelectedElement(null);
        setHasSelectedElement(false);
      } else {
        setHasSelectedElement(true);
      }
    } else {
      store.canvas.discardActiveObject();
      store.setSelectedElement(null);
      setHasSelectedElement(false);
      store.canvas.requestRenderAll();
    }
  };

  // 优化编辑模式变化处理
  useEffect(() => {
    if (!store.canvas) return;

    const isHandMode = store.editMode === "hand";
    store.canvas.selection = !isHandMode;

    if (isHandMode) {
      store.canvas.discardActiveObject();
      store.setSelectedElement(null);
      setHasSelectedElement(false);
    }

    // 批量设置对象属性
    store.canvas.getObjects().forEach((obj) => {
      obj.selectable = !isHandMode;
      obj.evented = !isHandMode;
    });

    store.canvas.requestRenderAll();
  }, [store.editMode]);

  // 监听画布缩放变化事件
  useEffect(() => {
    const handleCanvasZoomChange = (event: CustomEvent) => {
      setValue(event.detail);
    };

    window.addEventListener(
      "canvas-zoom-change",
      handleCanvasZoomChange as EventListener
    );
    return () => {
      window.removeEventListener(
        "canvas-zoom-change",
        handleCanvasZoomChange as EventListener
      );
    };
  }, []);

  // 优化渲染部分
  return (
    <Paper
      elevation={3}
      sx={{
        width: "100%",
        height: "calc(100vh - 260px)",
        overflow: "hidden",
        borderRadius: 2,
        position: "relative",
      }}
    >
      <Box
        id="grid-canvas-container"
        ref={boxRef}
        sx={(theme) => ({
          width: "100%",
          height: "100%",
          backgroundImage: `radial-gradient(circle at 15px 15px, ${theme.palette.grey[100]} 2px, transparent 0)`,
          backgroundSize: "35px 35px",
          backgroundPosition: "center center",
        })}
      >
        <MapInteractionCSS
          onChange={(newValue: any) => {
            setValue(newValue);
            store.updateCanvasScale(newValue.scale, newValue.translation);
          }}
          value={value}
          disablePan={store.editMode === "move"}
          disableZoom={store.editMode === "move"}
          defaultScale={store.canvasScale}
          minScale={0.1}
          maxScale={2}
          style={{
            width: "100%",
            height: "100%",
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              boxShadow: 3,
              borderRadius: 1,
              overflow: "hidden",
            }}
          >
            <canvas id="myCanvas" ref={canvasRef} />
          </Box>
        </MapInteractionCSS>
      </Box>
    </Paper>
  );
});
