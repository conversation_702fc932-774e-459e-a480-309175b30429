import AlignHorizontalCenterIcon from "@mui/icons-material/AlignHorizontalCenter";
import AlignHorizontalLeftIcon from "@mui/icons-material/AlignHorizontalLeft";
import AlignHorizontalRightIcon from "@mui/icons-material/AlignHorizontalRight";
import AlignVerticalBottomIcon from "@mui/icons-material/AlignVerticalBottom";
import AlignVerticalCenterIcon from "@mui/icons-material/AlignVerticalCenter";
import AlignVerticalTopIcon from "@mui/icons-material/AlignVerticalTop";
import DeleteIcon from "@mui/icons-material/Delete";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import LockIcon from "@mui/icons-material/Lock";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import Opacity from "@mui/icons-material/Opacity";
import {
  Box,
  IconButton,
  Popover,
  Slider,
  Stack,
  Tooltip,
  Typo<PERSON>,
  <PERSON>Field,
  Grid,
} from "@mui/material";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import FlipIcon from "@mui/icons-material/Flip";
import { fabric } from "fabric";

const BaseSetting = observer(({ element }) => {
  const store = React.useContext(StoreContext);
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleAlign = (alignType) => {
    store.alignElement(element.id, alignType);
  };

  const handleLock = () => {
    store.toggleLockElement(element.id);
  };

  const handleClone = () => {
    store.cloneElement(element.id);
  };

  const handleDelete = () => {
    store.deleteElement(element.id);
  };

  const handleFullscreen = () => {
    store.setElementFullscreen(element.id);
  };

  const handleOpacityClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleOpacityClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  // React.useEffect(() => {

  //   const handleKeyDown = (event: KeyboardEvent) => {
  //     // 检查是否有文本对象在编辑状态
  //     const activeObject = store.canvas.getActiveObject();

  //     if (activeObject instanceof fabric.IText && activeObject.isEditing) {
  //       return; // 如果是文本对象且正在编辑，直接返回，不处理任何键盘事件
  //     }

  //     if (!element.fabricObject || element.locked) return;
  //     if (event.key === "Delete" || event.key === "Backspace") {
  //       console.log("delete");
  //       handleDelete();
  //     }

  //     const MOVE_STEP = 1; // 每次移动1像素
  //     let deltaX = 0;
  //     let deltaY = 0;

  //     switch (event.key) {
  //       case "ArrowLeft":
  //         deltaX = -MOVE_STEP;
  //         break;
  //       case "ArrowRight":
  //         deltaX = MOVE_STEP;
  //         break;
  //       case "ArrowUp":
  //         deltaY = -MOVE_STEP;
  //         break;
  //       case "ArrowDown":
  //         deltaY = MOVE_STEP;
  //         break;
  //     }

  //     if (deltaX !== 0 || deltaY !== 0) {
  //       const updatedPlacement = { ...element.placement };
  //       updatedPlacement.x += deltaX;
  //       updatedPlacement.y += deltaY;

  //       element.fabricObject.set({
  //         left: updatedPlacement.x,
  //         top: updatedPlacement.y,
  //       });

  //       store.updateEditorElement({
  //         ...element,
  //         placement: updatedPlacement,
  //       });
  //       store.canvas?.renderAll();
  //     }
  //   };

  //   document.addEventListener("keydown", handleKeyDown);
  //   return () => {
  //     document.removeEventListener("keydown", handleKeyDown);
  //   };
  // }, [element, store]);

  const handleFlip = (flipType: "horizontal" | "vertical") => {
    store.flipElement(element.id, flipType);
  };

  const handleLayoutChange = (property: string, value: number) => {
    if (element.fabricObject) {
      const updatedPlacement = { ...element.placement };
      updatedPlacement[property] = value;

      if (property === "x" || property === "y") {
        element.fabricObject.set(property === "x" ? "left" : "top", value);
      } else if (property === "width" || property === "height") {
        console.log(value);
        element.fabricObject.set(property, value);
      } else if (property === "rotation") {
        element.fabricObject.set("angle", value);
      }
      element.placement = updatedPlacement;
      store.canvas.requestRenderAll();
      store.saveChange();
      // store.updateEditorElement({
      //   ...element,
      //   placement: updatedPlacement,
      // });
    }
  };

  return (
    <Box sx={{ height: "100%" }}>
      <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
        <Tooltip title={element.locked ? "Unlock" : "Lock"}>
          <IconButton onClick={handleLock}>
            {element.locked ? (
              <LockIcon fontSize="small" />
            ) : (
              <LockOpenIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title="Opacity">
          <IconButton onClick={handleOpacityClick}>
            <Opacity fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Clone">
          <IconButton onClick={handleClone}>
            <FileCopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Delete">
          <IconButton onClick={handleDelete}>
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Fullscreen">
          <IconButton onClick={handleFullscreen}>
            <FullscreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Flip Horizontal">
          <IconButton onClick={() => handleFlip("horizontal")}>
            <FlipIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Flip Vertical">
          <IconButton onClick={() => handleFlip("vertical")}>
            <FlipIcon fontSize="small" sx={{ transform: "rotate(90deg)" }} />
          </IconButton>
        </Tooltip>
      </Stack>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleOpacityClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box sx={{ p: 2, width: 250 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Slider
              value={element.opacity * 100}
              onChange={(_, newValue) => {
                store.updateElementOpacity(
                  element.id,
                  (newValue as number) / 100
                );
              }}
              aria-labelledby="opacity-slider"
              valueLabelDisplay="off"
              step={1}
              min={0}
              max={100}
              sx={{
                color: "#1976d2",
                "& .MuiSlider-thumb": {
                  width: 12,
                  height: 12,
                  transition: "0.3s cubic-bezier(.47,1.64,.41,.8)",
                  "&:before": {
                    boxShadow: "0 2px 12px 0 rgba(0,0,0,0.4)",
                  },
                  "&:hover, &.Mui-focusVisible": {
                    boxShadow: `0px 0px 0px 8px rgb(25 118 210 / 16%)`,
                  },
                },
                "& .MuiSlider-rail": {
                  opacity: 0.5,
                },
              }}
            />
            <Typography
              variant="body2"
              sx={{ minWidth: 35, textAlign: "right", color: "text.secondary" }}
            >
              {Math.round(element.opacity * 100)}%
            </Typography>
          </Stack>
        </Box>
      </Popover>
      <Typography variant="subtitle2" sx={{ mb: 1, color: "text.secondary" }}>
        Alignment
      </Typography>

      <Stack direction="row">
        {[
          {
            icon: AlignHorizontalLeftIcon,
            align: "left",
            tooltip: "Align Left",
          },
          {
            icon: AlignHorizontalCenterIcon,
            align: "center",
            tooltip: "Align Center",
          },
          {
            icon: AlignHorizontalRightIcon,
            align: "right",
            tooltip: "Align Right",
          },
          {
            icon: FormatAlignJustifyIcon,
            align: "justify",
            tooltip: "Justify",
          },
          { icon: AlignVerticalTopIcon, align: "top", tooltip: "Align Top" },
          {
            icon: AlignVerticalCenterIcon,
            align: "middle",
            tooltip: "Align Middle",
          },
          {
            icon: AlignVerticalBottomIcon,
            align: "bottom",
            tooltip: "Align Bottom",
          },
        ].map(({ icon: Icon, align, tooltip }, index) => (
          <Tooltip key={index} title={tooltip}>
            <IconButton onClick={() => handleAlign(align)}>
              <Icon fontSize="small" />
            </IconButton>
          </Tooltip>
        ))}
      </Stack>

      <Typography variant="subtitle2" sx={{ my: 1, color: "text.secondary" }}>
        Position
      </Typography>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          <TextField
            label="Position X"
            type="number"
            value={Math.round(element.placement?.x)}
            onChange={(e) => handleLayoutChange("x", Number(e.target.value))}
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label="Position Y"
            type="number"
            value={Math.round(element.placement?.y)}
            onChange={(e) => handleLayoutChange("y", Number(e.target.value))}
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label="Width"
            type="number"
            value={Math.round(element.placement?.width)}
            onChange={(e) =>
              handleLayoutChange("width", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label="Height"
            type="number"
            value={Math.round(element.placement?.height)}
            onChange={(e) =>
              handleLayoutChange("height", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            label="Rotation"
            type="number"
            value={Math.round(element.placement?.rotation)}
            onChange={(e) =>
              handleLayoutChange("rotation", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
      </Grid>
    </Box>
  );
});

export default BaseSetting;
