import { observer } from "mobx-react-lite";
import React, { useCallback, useEffect, useRef, useState } from "react";

import {
  Box,
  IconButton,
  TextField,
  Tooltip,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Divider,
  Paper,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";

import RedoIcon from "@mui/icons-material/Redo";
import UndoIcon from "@mui/icons-material/Undo";
import PanToolIcon from "@mui/icons-material/PanTool";
import NavigationIcon from "@mui/icons-material/Navigation";
import ZoomInIcon from "@mui/icons-material/ZoomIn";
import ZoomOutIcon from "@mui/icons-material/ZoomOut";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import EditIcon from "@mui/icons-material/Edit";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardIcon from "@mui/icons-material/Keyboard";
import { StoreContext } from "../store";
import { DownloadPopover, ResizeVideo, BackgroundSelector } from "./components";
import { ThemeToggle } from "../theme/ThemeToggle";
import { useHotkeys } from "react-hotkeys-hook";

// Constants
const DEFAULT_TITLE = "Untitled video";

// Common styles
const COMMON_STYLES = {
  navBarButton: {
    color: "text.secondary",
    "&:hover": {
      backgroundColor: "action.hover",
    },
  },
  buttonGroup: {
    display: "flex",
    alignItems: "center",
    gap: 0.5,
    height: "100%",
    px: 1,
  },
  divider: {
    height: 24,
    mx: 1,
  },
};

// 快捷键配置
interface ShortcutKey {
  key: string;
  description: string;
  category: string;
}

const SHORTCUT_KEYS: ShortcutKey[] = [
  { key: "Ctrl+Z", description: "撤销", category: "编辑" },
  { key: "Ctrl+Y", description: "重做", category: "编辑" },
  { key: "V", description: "移动模式", category: "工具" },
  { key: "H", description: "手型工具", category: "工具" },
  { key: "Ctrl++", description: "放大", category: "视图" },
  { key: "Ctrl+-", description: "缩小", category: "视图" },
  { key: "Ctrl+0", description: "适应大小", category: "视图" },
  { key: "Ctrl+S", description: "保存项目", category: "项目" },
  { key: "Ctrl+E", description: "导出视频", category: "项目" },
  { key: "Ctrl+K", description: "显示快捷键", category: "帮助" },
  { key: "Delete", description: "删除选中元素", category: "编辑" },
  { key: "Ctrl+D", description: "复制选中元素", category: "编辑" },
  { key: "Space", description: "播放/暂停", category: "播放" },
];

export const Navbar = observer(() => {
  const store = React.useContext(StoreContext);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [videoTitle, setVideoTitle] = useState(
    store.projectName || DEFAULT_TITLE
  );
  const titleInputRef = useRef<HTMLInputElement>(null);
  const [shortcutsDialogOpen, setShortcutsDialogOpen] = useState(false);

  // 获取撤销/重做操作的类型，用于提示
  const undoActionType = store.getUndoActionType?.();
  const redoActionType = store.getRedoActionType?.();

  const handleUndo = useCallback(() => {
    store.undo?.();
  }, [store]);

  const handleRedo = useCallback(() => {
    store.redo?.();
  }, [store]);

  // 处理画布缩放
  const handleZoomIn = useCallback(() => {
    const newValue = store.zoomIn();
    // 触发CanvasContainer中的MapInteractionCSS组件更新
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  const handleZoomOut = useCallback(() => {
    const newValue = store.zoomOut();
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  const handleResetZoom = useCallback(() => {
    const newValue = store.resetZoom();
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  // 处理编辑模式切换
  const handleEditModeChange = useCallback(
    (_: React.MouseEvent<HTMLElement>, newMode: "move" | "hand" | null) => {
      // 如果newMode为null（用户点击当前已选中的按钮），保持当前模式不变
      if (newMode !== null) {
        store.setEditMode(newMode);
      }
    },
    [store]
  );

  useEffect(() => {
    // Update title when store project name changes
    if (store.projectName && store.projectName !== videoTitle) {
      setVideoTitle(store.projectName);
    }
  }, [store.projectName]);

  const handleTitleClick = useCallback(() => {
    setIsEditingTitle(true);
    setTimeout(() => {
      titleInputRef.current?.focus();
      titleInputRef.current?.select();
    }, 0);
  }, []);

  const handleTitleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newTitle = event.target.value;
      console.log("Title changed to:", newTitle);
      setVideoTitle(newTitle);
      // 实时更新store中的projectName
      store.setProjectName?.(newTitle);
    },
    [store]
  );

  const saveTitle = useCallback(
    (title: string) => {
      const finalTitle = title.trim() === "" ? DEFAULT_TITLE : title;
      setVideoTitle(finalTitle);
      store.setProjectName?.(finalTitle);
    },
    [store]
  );

  const handleTitleBlur = useCallback(() => {
    setIsEditingTitle(false);
    saveTitle(videoTitle);
  }, [videoTitle, saveTitle]);

  const handleTitleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Enter") {
        setIsEditingTitle(false);
        saveTitle(videoTitle);
      } else if (event.key === "Escape") {
        // Cancel editing and revert to previous title
        setIsEditingTitle(false);
        setVideoTitle(store.projectName || DEFAULT_TITLE);
      }
    },
    [videoTitle, store.projectName, saveTitle]
  );

  // 处理快捷键对话框
  const handleOpenShortcutsDialog = useCallback(() => {
    setShortcutsDialogOpen(true);
  }, []);

  const handleCloseShortcutsDialog = useCallback(() => {
    setShortcutsDialogOpen(false);
  }, []);

  useHotkeys(
    "ctrl+v",
    () => {
      if (store.editMode !== "move") store.setEditMode("move");
    },
    { enabled: !isEditingTitle },
    [store, isEditingTitle]
  );

  useHotkeys(
    "ctrl+h",
    () => {
      if (store.editMode !== "hand") store.setEditMode("hand");
    },
    { enabled: !isEditingTitle },
    [store, isEditingTitle]
  );

  useHotkeys(
    "ctrl+=",
    (e) => {
      e.preventDefault();
      handleZoomIn();
    },
    { enableOnFormTags: ["INPUT", "TEXTAREA"] },
    [handleZoomIn]
  );

  useHotkeys(
    "ctrl+-",
    (e) => {
      e.preventDefault();
      handleZoomOut();
    },
    { enableOnFormTags: ["INPUT", "TEXTAREA"] },
    [handleZoomOut]
  );

  useHotkeys(
    "ctrl+0",
    (e) => {
      e.preventDefault();
      handleResetZoom();
    },
    { enableOnFormTags: ["INPUT", "TEXTAREA"] },
    [handleResetZoom]
  );

  return (
    <>
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "62px",
          zIndex: 205,
          display: "flex",
          justifyContent: "space-between",
          maxWidth: "98%",
          margin: "0 auto", // Center the navbar
          borderRadius: "0 0 8px 8px", // 圆角只在底部
          backdropFilter: "blur(8px)", // 毛玻璃效果
          backgroundColor: (theme) =>
            theme.palette.mode === "dark"
              ? alpha(theme.palette.background.paper, 0.85)
              : alpha(theme.palette.background.paper, 0.95),
        }}
      >
        {/* 左侧区域：Logo、撤销/重做、导出 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            height: "100%",
          }}
        >
          {/* Logo */}
          <Box
            sx={{
              height: 48,
              width: 48,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: 1,
              ml: 1,
            }}
          >
            <img
              src="/assets/logo.png"
              alt="Video Editor Logo"
              style={{ height: 32, width: 32 }}
            />
          </Box>

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />
          {/* 项目标题 */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
            }}
          >
            {isEditingTitle ? (
              <Box sx={{ display: "flex", alignItems: "center", minWidth: 80 }}>
                <TextField
                  inputRef={titleInputRef}
                  value={videoTitle}
                  onChange={handleTitleChange}
                  onBlur={handleTitleBlur}
                  onKeyDown={handleTitleKeyDown}
                  variant="outlined"
                  size="small"
                  autoFocus
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                    },
                  }}
                />
                <Box sx={{ display: "flex", ml: 1 }}>
                  <IconButton
                    size="small"
                    onClick={() => {
                      saveTitle(videoTitle);
                      setIsEditingTitle(false);
                    }}
                  >
                    <CheckIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setVideoTitle(store.projectName || DEFAULT_TITLE);
                      setIsEditingTitle(false);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  border: "1px solid transparent",
                  borderRadius: 2,
                  px: 1.5,
                  py: 0.5,
                  "&:hover": {
                    borderColor: "divider",
                    backgroundColor: "action.hover",
                  },
                }}
                onClick={handleTitleClick}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    cursor: "pointer",
                    mr: 1,
                  }}
                >
                  {videoTitle}
                </Typography>
                {/* <EditIcon
                  fontSize="small"
                  sx={{ color: "text.secondary", opacity: 0.7 }}
                /> */}
              </Box>
            )}
          </Box>
          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />
          {/* 撤销/重做按钮组 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <Tooltip
              title={`撤销 (Ctrl+Z)${
                undoActionType ? ` - ${undoActionType}` : ""
              }`}
              arrow
            >
              <span>
                <IconButton
                  onClick={handleUndo}
                  size="small"
                  disabled={!undoActionType}
                  sx={COMMON_STYLES.navBarButton}
                >
                  <UndoIcon sx={{ fontSize: 20 }} />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip
              title={`重做 (Ctrl+Y)${
                redoActionType ? ` - ${redoActionType}` : ""
              }`}
              arrow
            >
              <span>
                <IconButton
                  onClick={handleRedo}
                  size="small"
                  disabled={!redoActionType}
                  sx={COMMON_STYLES.navBarButton}
                >
                  <RedoIcon sx={{ fontSize: 20 }} />
                </IconButton>
              </span>
            </Tooltip>
          </Box>

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 导出按钮 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <DownloadPopover />
          </Box>
        </Box>

        {/* 中间区域：项目标题、编辑模式切换、缩放控制 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            flex: 1,
          }}
        >
          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 编辑模式切换按钮组 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <ToggleButtonGroup
              value={store.editMode}
              exclusive
              onChange={handleEditModeChange}
              aria-label="edit mode"
              size="small"
              sx={{
                height: 32,
                "& .MuiToggleButton-root": {
                  border: "1px solid",
                  borderColor: "divider",
                  px: 1.5,
                  "&.Mui-selected": {
                    backgroundColor: (theme) =>
                      theme.palette.mode === "dark"
                        ? alpha(theme.palette.primary.main, 0.2)
                        : alpha(theme.palette.primary.main, 0.1),
                    color: "primary.main",
                    borderColor: "primary.main",
                  },
                },
              }}
            >
              <Tooltip title="移动模式 (V)" arrow>
                <ToggleButton value="move" aria-label="move mode">
                  <NavigationIcon fontSize="small" />
                </ToggleButton>
              </Tooltip>
              <Tooltip title="手型工具 (H)" arrow>
                <ToggleButton value="hand" aria-label="hand tool mode">
                  <PanToolIcon fontSize="small" />
                </ToggleButton>
              </Tooltip>
            </ToggleButtonGroup>
          </Box>

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 缩放控制按钮组 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <Tooltip title="放大 (Ctrl++)" arrow>
              <IconButton
                onClick={handleZoomIn}
                size="small"
                sx={COMMON_STYLES.navBarButton}
              >
                <ZoomInIcon sx={{ fontSize: 24 }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="缩小 (Ctrl+-)" arrow>
              <IconButton
                onClick={handleZoomOut}
                size="small"
                sx={COMMON_STYLES.navBarButton}
              >
                <ZoomOutIcon sx={{ fontSize: 24 }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="适应大小 (Ctrl+0)" arrow>
              <IconButton
                onClick={handleResetZoom}
                size="small"
                sx={COMMON_STYLES.navBarButton}
              >
                <RestartAltIcon sx={{ fontSize: 24 }} />
              </IconButton>
            </Tooltip>
          </Box>
          {/* 视频尺寸和背景选择 */}
          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          <Box sx={COMMON_STYLES.buttonGroup}>
            <ResizeVideo />
            <BackgroundSelector />
          </Box>
        </Box>

        {/* 右侧区域：视频尺寸、背景选择、主题切换 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            height: "100%",
          }}
        >
          {/* 主题切换 */}
          <Box sx={{ ...COMMON_STYLES.buttonGroup, mr: 1 }}>
            <ThemeToggle size="small" />
            <Tooltip title="快捷键 (Ctrl+K)" arrow>
              <IconButton
                size="small"
                sx={COMMON_STYLES.navBarButton}
                onClick={handleOpenShortcutsDialog}
              >
                <KeyboardIcon sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* 快捷键对话框 */}
      <Dialog
        open={shortcutsDialogOpen}
        onClose={handleCloseShortcutsDialog}
        maxWidth="md"
        // 使用 sx 替代 PaperProps
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: 2,
            minWidth: 500,
          },
        }}
      >
        <DialogTitle
          sx={{
            borderBottom: "1px solid",
            borderColor: "divider",
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <KeyboardIcon />
            <Typography variant="h6">键盘快捷键</Typography>
          </Box>
          <IconButton size="small" onClick={handleCloseShortcutsDialog}>
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
            {/* 按类别分组显示快捷键 */}
            {Array.from(new Set(SHORTCUT_KEYS.map((k) => k.category))).map(
              (category) => (
                <Box key={category} sx={{ minWidth: 200 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{ mb: 1, color: "primary.main", fontWeight: 600 }}
                  >
                    {category}
                  </Typography>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    {SHORTCUT_KEYS.filter((k) => k.category === category).map(
                      (shortcut, index) => (
                        <Box
                          key={index}
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Typography variant="body2">
                            {shortcut.description}
                          </Typography>
                          <Box
                            sx={{
                              backgroundColor: "action.hover",
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              minWidth: 60,
                              textAlign: "center",
                              fontFamily: "monospace",
                              fontSize: "0.8rem",
                              fontWeight: "medium",
                              border: "1px solid",
                              borderColor: "divider",
                            }}
                          >
                            {shortcut.key}
                          </Box>
                        </Box>
                      )
                    )}
                  </Box>
                </Box>
              )
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
});
