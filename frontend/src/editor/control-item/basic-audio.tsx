import { Box, Typography } from "@mui/material";
import PlaybackSpeedControl from "./PlaybackSpeedControl";
import VolumeControl from "./VolumeControl";
const BasicAudio = ({ element }) => {
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Audio
        </Typography>
      </Box>
      <Box sx={{ mb: 1, px: 1 }}>
        <VolumeControl element={element} />
      </Box>
      <Box sx={{ px: 1 }}>
        <PlaybackSpeedControl element={element} />
      </Box>
    </Box>
  );
};

export default BasicAudio;
