import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>lider,
  TextField,
  Grid,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../../store";
import { ShapeEditorElement, ShapeType } from "../../types";
import BaseSetting from "../setting/BaseSetting";

interface BasicShapeProps {
  element: ShapeEditorElement;
}

const BasicShape = observer(({ element }: BasicShapeProps) => {
  const store = React.useContext(StoreContext);
  const [fill, setFill] = useState(element.properties.fill || "#9e9e9e");
  const [stroke, setStroke] = useState(element.properties.stroke || "#757575");
  const [strokeWidth, setStrokeWidth] = useState(
    element.properties.strokeWidth || 1
  );
  const [borderRadius, setBorderRadius] = useState(
    element.properties.borderRadius || 0
  );

  useEffect(() => {
    if (element) {
      setFill(element.properties.fill || "#9e9e9e");
      setStroke(element.properties.stroke || "#757575");
      setStrokeWidth(element.properties.strokeWidth || 1);
      setBorderRadius(element.properties.borderRadius || 0);
    }
  }, [element]);

  const handleFillChange = (color: string) => {
    setFill(color);
    updateShapeProperties({ fill: color });
  };

  const handleStrokeChange = (color: string) => {
    setStroke(color);
    updateShapeProperties({ stroke: color });
  };

  const handleStrokeWidthChange = (event: Event, value: number | number[]) => {
    const newValue = value as number;
    setStrokeWidth(newValue);
    updateShapeProperties({ strokeWidth: newValue });
  };

  const handleBorderRadiusChange = (event: Event, value: number | number[]) => {
    const newValue = value as number;
    setBorderRadius(newValue);
    updateShapeProperties({ borderRadius: newValue });
  };

  const handleShapeTypeChange = (
    event: React.ChangeEvent<{ value: unknown }>
  ) => {
    const newShapeType = event.target.value as ShapeType;
    updateShapeProperties({ shapeType: newShapeType });
  };

  const updateShapeProperties = (
    properties: Partial<ShapeEditorElement["properties"]>
  ) => {
    if (!element) return;

    const updatedElement = {
      ...element,
      properties: {
        ...element.properties,
        ...properties,
      },
    };

    store.updateEditorElement(updatedElement);
  };

  if (!element) return null;

  return (
    <Box sx={{ p: 2, width: "100%", height: "100%", overflow: "auto" }}>
      {/* 基本设置部分 */}
      <BaseSetting element={element} />

      <Divider sx={{ my: 2 }} />

      <Typography variant="h6" gutterBottom>
        图形属性
      </Typography>

      <Divider sx={{ my: 2 }} />

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth size="small">
            <InputLabel>图形类型</InputLabel>
            <Select
              value={element.properties.shapeType}
              label="图形类型"
              onChange={handleShapeTypeChange as any}
            >
              <MenuItem value="rect">矩形</MenuItem>
              <MenuItem value="roundedRect">圆角矩形</MenuItem>
              <MenuItem value="circle">圆形</MenuItem>
              <MenuItem value="ellipse">椭圆</MenuItem>
              <MenuItem value="triangle">三角形</MenuItem>
              <MenuItem value="line">直线</MenuItem>
              <MenuItem value="pentagon">五边形</MenuItem>
              <MenuItem value="hexagon">六边形</MenuItem>
              <MenuItem value="octagon">八边形</MenuItem>
              <MenuItem value="parallelogram">平行四边形</MenuItem>
              <MenuItem value="arch">拱形</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Typography gutterBottom>填充颜色</Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1,
                bgcolor: fill,
                border: "1px solid #ccc",
                mr: 1,
              }}
            />
            <TextField
              size="small"
              value={fill}
              onChange={(e) => handleFillChange(e.target.value)}
              sx={{ flex: 1 }}
              type="color"
            />
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Typography gutterBottom>边框颜色</Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1,
                bgcolor: stroke,
                border: "1px solid #ccc",
                mr: 1,
              }}
            />
            <TextField
              size="small"
              value={stroke}
              onChange={(e) => handleStrokeChange(e.target.value)}
              sx={{ flex: 1 }}
              type="color"
            />
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Typography gutterBottom>边框宽度: {strokeWidth}px</Typography>
          <Slider
            value={strokeWidth}
            onChange={handleStrokeWidthChange}
            min={0}
            max={20}
            step={1}
          />
        </Grid>

        {(element.properties.shapeType === "rect" ||
          element.properties.shapeType === "roundedRect") && (
          <Grid item xs={12}>
            <Typography gutterBottom>圆角半径: {borderRadius}px</Typography>
            <Slider
              value={borderRadius}
              onChange={handleBorderRadiusChange}
              min={0}
              max={50}
              step={1}
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );
});

export default BasicShape;
