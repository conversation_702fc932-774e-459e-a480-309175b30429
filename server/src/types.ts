export interface CanvasState {
  width: number;
  height: number;
  backgroundColor: string;
  elements: CanvasElement[];
  outputFormat?: OutputFormat;
}

export interface OutputFormat {
  codec: string;
  format: string;
  quality: 'low' | 'medium' | 'high';
  frameRate: number;
}

export interface CanvasElement {
  type: 'video' | 'image' | 'audio' | 'text';
  id: string;
  properties: any;
  opacity?: number;
  placement?: ElementPlacement;
  timeFrame: TimeFrame;
  effects?: Effect[];
  border?: Border;
}

export interface ElementPlacement {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  scaleX: number;
  scaleY: number;
  flipX?: boolean;
  flipY?: boolean;
}

export interface TimeFrame {
  start: number;
  end: number;
}

export interface Effect {
  type: string;
  params: any;
}

export interface Border {
  color: string;
  width: number;
  style: string;
}

export interface TaskProgress {
  taskId: string;
  progress: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface VideoGenerationResponse {
  taskId: string;
}

export interface ProgressResponse {
  progress: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
} 