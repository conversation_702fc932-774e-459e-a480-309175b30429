import { FFMPEG_CONFIG } from "./config";
import { promisify } from "util";
import { exec } from "child_process";
import { CanvasState, MediaElement, Caption } from "./types";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// Promisified version of exec for async/await usage
const execAsync = promisify(exec);

/**
 * Utility class for FFmpeg-related operations
 * Provides helper methods for validation, file operations, and common FFmpeg tasks
 */
export class FFmpegUtils {
  /**
   * Validates and normalizes a CRF (Constant Rate Factor) value
   * @param crf - CRF value to validate
   * @returns Normalized CRF value between 0 and 51
   */
  static validateCrf(crf: number): number {
    return Math.max(0, Math.min(51, Math.round(crf)));
  }

  /**
   * Validates a canvas state object for processing
   * @param canvasState - Canvas state to validate
   * @throws Error if the canvas state is invalid
   */
  static validateCanvasState(canvasState: CanvasState): void {
    if (!canvasState.width || !canvasState.height) {
      throw new Error("Canvas dimensions must be specified");
    }
    if (!canvasState.elements || !Array.isArray(canvasState.elements)) {
      throw new Error("Elements array must be provided");
    }

    // Validate dimensions
    if (
      canvasState.width > FFMPEG_CONFIG.MAX_DIMENSION ||
      canvasState.height > FFMPEG_CONFIG.MAX_DIMENSION
    ) {
      throw new Error(
        `Canvas dimensions exceed maximum limit of ${FFMPEG_CONFIG.MAX_DIMENSION}px`
      );
    }

    // Validate duration
    const duration =
      Math.max(...canvasState.elements.map((el) => el.timeFrame.end)) / 1000;
    if (duration > FFMPEG_CONFIG.MAX_DURATION) {
      throw new Error(
        `Video duration exceeds maximum limit of ${FFMPEG_CONFIG.MAX_DURATION} seconds`
      );
    }

    // Validate elements
    canvasState.elements.forEach((element, index) => {
      if (!element.type || !element.properties) {
        throw new Error(
          `Invalid element at index ${index}: missing type or properties`
        );
      }
      if (
        element.type !== "text" &&
        element.type !== "audio" &&
        !element.placement
      ) {
        throw new Error(
          `Invalid element at index ${index}: missing placement data`
        );
      }
      if (
        ["image", "video", "audio"].includes(element.type) &&
        !element.properties.src
      ) {
        throw new Error(`Element at index ${index} missing src property`);
      }
    });
  }

  /**
   * Checks if a media file has an audio track
   * @param filePath - Path to the media file
   * @returns Promise resolving to true if the file has audio, false otherwise
   */
  static async hasAudioTrack(filePath: string): Promise<boolean> {
    try {
      // Use ffprobe to check for audio streams
      const command = `ffprobe -v quiet -select_streams a -show_streams "${filePath}"`;

      // Execute the command and get the result
      const { stdout } = await execAsync(command);

      // If stdout contains data, the file has audio streams
      return Boolean(stdout && stdout.trim().length > 0);
    } catch (error) {
      console.warn(`Failed to check audio tracks for ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Gets encoding parameters based on quality setting
   * @param quality - Quality setting ("low", "medium", "high")
   * @returns Encoding parameters with preset, crf, and profile
   */
  static getEncodingParams(quality: string) {
    // Get preset from config or fall back to medium quality
    const preset =
      FFMPEG_CONFIG.ENCODING_PRESETS[
        quality as keyof typeof FFMPEG_CONFIG.ENCODING_PRESETS
      ] || FFMPEG_CONFIG.ENCODING_PRESETS.medium;

    // Return a copy with validated CRF value
    return {
      ...preset,
      crf: this.validateCrf(preset.crf),
    };
  }

  /**
   * Generates a timestamp string for unique filenames
   * @returns Formatted timestamp string
   */
  static generateTimestamp(): string {
    return new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .replace("Z", "");
  }

  /**
   * Escapes a file path for use in shell commands
   * @param path - File path to escape
   * @returns Escaped file path
   */
  static escapeFilePath(path: string): string {
    return path.replace(/"/g, '\\"');
  }

  /**
   * Optimizes a filter chain by grouping similar operations
   * @param filters - Array of filter commands
   * @returns Optimized array of filter commands
   */
  static optimizeFilterChain(filters: string[]): string[] {
    // Group filters by type for more efficient processing
    const grouped: { [key: string]: string[] } = {
      scale: [], // Scaling operations
      rotate: [], // Rotation operations
      colorspace: [], // Format and colorspace operations
      effects: [], // Visual effects
      overlay: [], // Overlay operations
    };

    // Sort filters into groups
    filters.forEach((filter) => {
      if (filter.includes("scale=")) {
        grouped.scale.push(filter);
      } else if (filter.includes("rotate=")) {
        grouped.rotate.push(filter);
      } else if (filter.includes("format=") || filter.includes("colorspace=")) {
        grouped.colorspace.push(filter);
      } else if (filter.includes("overlay=")) {
        grouped.overlay.push(filter);
      } else {
        grouped.effects.push(filter);
      }
    });

    // Return filters in optimized order
    return [
      ...grouped.scale,
      ...grouped.rotate,
      ...grouped.colorspace,
      ...grouped.effects,
      ...grouped.overlay,
    ];
  }

  /**
   * Creates a temporary SRT file from captions
   * @param captions Array of captions
   * @returns Path to the created SRT file
   */
  static createSubtitleFile(captions: Caption[]): string {
    if (!captions || captions.length === 0) {
      return "";
    }

    // Create the SRT content
    let srtContent = "";
    captions.forEach((caption, index) => {
      // Convert HH:MM:SS format to HH:MM:SS,mmm format required by SRT
      const startTime = caption.startTime.replace(/(\d\d:\d\d:\d\d)/, "$1,000");
      const endTime = caption.endTime.replace(/(\d\d:\d\d:\d\d)/, "$1,000");

      srtContent += `${index + 1}\n`;
      srtContent += `${startTime} --> ${endTime}\n`;
      srtContent += `${caption.text}\n\n`;
    });

    // Create a temporary file
    const tempDir = os.tmpdir();
    const subtitlePath = path.join(
      tempDir,
      `subtitles_${this.generateTimestamp()}.srt`
    );
    console.log(srtContent);
    fs.writeFileSync(subtitlePath, srtContent, "utf8");

    return subtitlePath;
  }

  /**
   * Generate subtitle filter command
   * @param subtitlePath Path to the SRT file
   * @param canvasWidth Canvas width
   * @param canvasHeight Canvas height
   * @returns FFmpeg subtitle filter string
   */
  static generateSubtitleFilter(
    subtitlePath: string,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    if (!subtitlePath) {
      return "";
    }

    const fontSize = Math.max(16, Math.round(canvasHeight * 0.035));
    const bottomMargin = Math.round(canvasHeight * 0.02);

    // 设置字幕样式参数
    const subtitleStyles = [
      `FontSize=${fontSize}`,
      `FontName=Roboto`, // 默认字体
      `PrimaryColour=&HFFFFFF&`, // 白色
      `SecondaryColour=&H0000FF&`, // 红色
      `OutlineColour=&H40000000`, // 黑色
      `BackColour=&H0000FF&`, // 黑色
      `Bold=1`, // 启用粗体
      `Italic=0`, // 禁用斜体
      `Underline=0`, // 禁用下划线
      `Alignment=2`, // 底部居中对齐
      `BorderStyle=1`,
      `Outline=1`,
      `Shadow=0`,
      `MarginV=${bottomMargin}`,
      `MarginL=5`, // 左边距
      `MarginR=5`, // 右边距
      `Spacing=1`, // 字符间距
    ].join(",");

    return `subtitles='${this.escapeFilePath(
      subtitlePath
    )}':force_style='${subtitleStyles}'`;
  }
}
