import { FFmpegUtils } from "../utils";
import { TextFilterGenerator } from "./filters/TextFilterGenerator";
import { ImageFilterGenerator } from "./filters/ImageFilterGenerator";
import { VideoFilterGenerator } from "./filters/VideoFilterGenerator";
import { AudioFilterGenerator } from "./filters/AudioFilterGenerator";
import { MediaOverlayFilterGenerator } from "./filters/MediaOverlayFilterGenerator";
import { MediaElement } from "../types";
import { ProcessingContext, ProcessingResult } from "./types";
import { BaseProcessor } from "./BaseProcessor";

/**
 * ElementProcessor handles the conversion of different media elements into FFmpeg filter commands.
 * It processes audio, video, image, and text elements to generate appropriate filters
 * for FFmpeg's complex filter system, allowing for the composition of multimedia content.
 */
export class ElementProcessor extends BaseProcessor {
  private readonly textFilterGenerator: TextFilterGenerator;
  private readonly imageFilterGenerator: ImageFilterGenerator;
  private readonly videoFilterGenerator: VideoFilterGenerator;
  private readonly audioFilterGenerator: AudioFilterGenerator;
  private readonly mediaOverlayFilterGenerator: MediaOverlayFilterGenerator;

  constructor() {
    super();
    this.textFilterGenerator = new TextFilterGenerator();
    this.imageFilterGenerator = new ImageFilterGenerator();
    this.videoFilterGenerator = new VideoFilterGenerator();
    this.audioFilterGenerator = new AudioFilterGenerator();
    this.mediaOverlayFilterGenerator = new MediaOverlayFilterGenerator(
      this.textFilterGenerator
    );
  }

  /**
   * Process a media element based on its type
   * @param element - The media element to process
   * @param context - The processing context
   * @returns The processing result
   */
  protected processElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    switch (element.type) {
      case "audio":
        return this.processAudioElement(element, context);
      case "video":
        return this.processVideoElement(element, context);
      case "image":
        return this.processImageElement(element, context);
      case "text":
        return this.processTextElement(element, context);
      default:
        throw new Error(`Unsupported element type: ${element.type}`);
    }
  }

  /**
   * Process audio element to add it to the FFmpeg command
   * 优化版本：处理音频元素，支持重用相同的输入源
   *
   * @param element - The audio element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the last video label and updated audio stream count
   */
  processAudioElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    if (!context.hasAudioContent) {
      return {
        lastVideoLabel: context.lastVideoLabel || "bg",
        audioStreamCount: context.audioStreamCount,
      };
    }

    // 添加媒体输入源，如果是相同的源会自动重用
    const audioInputIndex = this.addMediaInput(context.inputs, element);

    // 获取mediaStartTime属性，用于音频同步
    const mediaStartTime = element.properties.mediaStartTime
      ? Number(element.properties.mediaStartTime)
      : 0;

    console.log(
      `处理音频元素 ${element.id}，使用输入源索引: ${audioInputIndex}，mediaStartTime: ${mediaStartTime}`
    );

    const audioFilter = this.audioFilterGenerator.generateFilter(
      audioInputIndex,
      element,
      context.startTime,
      context.elementDuration,
      context.index,
      context.duration,
      context.audioStreamCount
    );

    // 直接添加音频过滤器，现在它已经包含输出标签[a${audioStreamCount}]
    context.audioFilters.push(audioFilter);

    return {
      lastVideoLabel: context.lastVideoLabel || "bg",
      audioStreamCount: context.audioStreamCount + 1,
    };
  }

  /**
   * Process video element to add it to the FFmpeg command
   * 优化版本：处理视频元素，支持重用相同的输入源
   *
   * @param element - The video element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and updated audio stream count
   */
  processVideoElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    // 添加媒体输入源，如果是相同的源会自动重用
    const mediaInputIndex = this.addMediaInput(context.inputs, element);
    console.log(
      `处理视频元素 ${element.id}，使用输入源索引: ${mediaInputIndex}`
    );

    // 处理视频元素的音频部分
    if (
      !context.skipAudioProcessing &&
      context.audioElements?.[context.index] &&
      context.hasAudioContent
    ) {
      // 获取mediaStartTime属性，用于音频同步
      const mediaStartTime = element.properties.mediaStartTime
        ? Number(element.properties.mediaStartTime)
        : 0;

      console.log(
        `处理视频元素 ${element.id} 的音频部分，mediaStartTime: ${mediaStartTime}`
      );

      // 使用相同的输入源索引处理音频
      const audioFilter = this.audioFilterGenerator.generateFilter(
        mediaInputIndex,
        element,
        context.startTime,
        context.elementDuration,
        context.duration,
        context.audioStreamCount
      );

      // 确保音频过滤器有输出标签
      if (!audioFilter.endsWith(`[a${context.audioStreamCount}]`)) {
        context.audioFilters.push(
          `${audioFilter}[a${context.audioStreamCount}]`
        );
      } else {
        context.audioFilters.push(audioFilter);
      }

      context.audioStreamCount++;
    }

    // 处理视频部分
    const videoFilter = this.videoFilterGenerator.generateFilter(
      mediaInputIndex,
      element,
      context.elementDuration,
      context.index
    );

    // 从element.properties中获取mediaStartTime
    const mediaStartTime = element.properties.mediaStartTime
      ? Number(element.properties.mediaStartTime)
      : 0;

    // 更新正则表达式以匹配新的trim和setpts格式
    const trimmedVideoFilter = videoFilter.replace(
      /trim=start=[^:]+:duration=[^,]+,setpts=PTS-STARTPTS\+[^/]+\/TB/,
      `trim=start=${mediaStartTime.toFixed(3)}:duration=${
        context.duration
      },setpts=PTS-STARTPTS`
    );

    context.filterComplex.push(trimmedVideoFilter);

    this.applyOverlay(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.duration,
      "vid",
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Process image element to add it to the FFmpeg command
   * 优化版本：处理图片元素，支持重用相同的输入源
   *
   * @param element - The image element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and audio stream count (0 for images)
   */
  processImageElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    this.validateElement(element, context.index);

    // 添加媒体输入源，如果是相同的源会自动重用
    const mediaInputIndex = this.addMediaInput(context.inputs, element);
    console.log(
      `处理图片元素 ${element.id}，使用输入源索引: ${mediaInputIndex}`
    );

    const imageFilter = this.imageFilterGenerator.generateFilter(
      mediaInputIndex,
      element,
      context.startTime,
      context.duration,
      context.index
    );

    context.filterComplex.push(imageFilter);

    this.applyOverlay(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.elementDuration,
      "img",
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Process text element to add it to the FFmpeg command
   *
   * @param element - The text element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and audio stream count (0 for text)
   */
  processTextElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    this.applyOverlay(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.duration,
      "text",
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Apply media overlay effect to combine the element with the existing video stream
   *
   * @param filterComplex - Array of complex filters to be modified
   * @param element - The element to overlay
   * @param index - Current element index in the timeline
   * @param canvasWidth - Width of the output canvas in pixels
   * @param canvasHeight - Height of the output canvas in pixels
   * @param duration - Total duration of the output video
   * @param mediaType - Type of media being overlaid ('vid', 'img', or 'text')
   * @param lastVideoLabel - Label of the last processed video element
   */
  private applyOverlay(
    filterComplex: string[],
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    mediaType: string,
    lastVideoLabel: string = `v${index - 1}`
  ): void {
    // Ensure we have a valid lastVideoLabel, default to "bg" if not
    const validVideoLabel = lastVideoLabel === `v-1` ? "bg" : lastVideoLabel;

    filterComplex.push(
      ...this.mediaOverlayFilterGenerator.generateMediaOverlayFilters(
        element,
        index,
        canvasWidth,
        canvasHeight,
        duration,
        mediaType
      )
    );

    filterComplex.push(
      `[${validVideoLabel}][${mediaType}_timed${index}]overlay=format=auto,format=yuv420p[v${index}]`
    );
  }
}
