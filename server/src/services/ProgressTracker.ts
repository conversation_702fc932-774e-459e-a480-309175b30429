import { ChildProcess } from "child_process";
import logger from "../utils/logger";

export interface ProgressInfo {
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  progress: number;
  currentFrame: number | null;
  totalFrames: number | null;
  fps: number | null;
  speed: number | null;
  error?: string;
  lastUpdate: number;
  stage?: string;
}

export class ProgressTracker {
  private progressMap = new Map<string, ProgressInfo>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Cleanup stale tasks (older than 1 hour) every 5 minutes
    this.cleanupInterval = setInterval(() => {
      const oneHourAgo = Date.now() - 3600000;
      for (const [taskId, info] of this.progressMap.entries()) {
        if (info.lastUpdate < oneHourAgo) {
          logger.info(`Cleaning up stale task ${taskId}`);
          this.progressMap.delete(taskId);
        }
      }
    }, 300000);
  }

  public initializeTask(taskId: string): void {
    this.progressMap.set(taskId, {
      status: "pending",
      progress: 0,
      currentFrame: null,
      totalFrames: null,
      fps: null,
      speed: null,
      lastUpdate: Date.now(),
      stage: "initializing",
    });
    logger.info(`Initialized new task ${taskId}`);
  }

  public getProgress(taskId: string): ProgressInfo | undefined {
    return this.progressMap.get(taskId);
  }

  public updateCommandGenerationProgress(
    taskId: string,
    stage: string,
    progress: number
  ): void {
    const currentInfo = this.progressMap.get(taskId);
    if (!currentInfo) return;

    // Command generation is about 20% of the whole process
    // 使用更小的比例用于命令生成，留出更多空间给渲染过程
    const adjustedProgress = Math.floor(progress * 0.2);

    this.progressMap.set(taskId, {
      ...currentInfo,
      status: "processing",
      progress: adjustedProgress,
      stage,
      lastUpdate: Date.now(),
    });

    logger.debug(
      `Command generation progress update for task ${taskId}: ${stage} - ${adjustedProgress}%`
    );
  }

  public trackFFmpegProgress(taskId: string, ffmpeg: ChildProcess): void {
    let duration: number | null = null;
    let totalFrames: number | null = null;
    let lastTimeProgress = 0;
    let wasCancelled = false;
    let hasStartedProcessing = false;
    let lastProgressUpdate = Date.now();
    let framesSinceLastProgressUpdate = 0;
    let secondsPassed = 0;

    // 创建进度阶段定义
    const renderingStages = [
      { threshold: 10, name: "initializing" },
      { threshold: 25, name: "video processing" },
      { threshold: 50, name: "audio processing" },
      { threshold: 75, name: "effect rendering" },
      { threshold: 95, name: "generating" },
    ];

    ffmpeg.stderr?.on("data", (data: Buffer) => {
      const line = data.toString();

      // 如果还没开始处理，设置初始状态
      if (!hasStartedProcessing) {
        hasStartedProcessing =
          line.includes("frame=") ||
          line.includes("time=") ||
          line.includes("stream mapping");

        if (hasStartedProcessing) {
          const currentInfo = this.progressMap.get(taskId);
          if (currentInfo) {
            this.progressMap.set(taskId, {
              ...currentInfo,
              status: "processing",
              stage: "initializing",
              progress: 20, // 命令生成完成后，开始渲染阶段
              lastUpdate: Date.now(),
            });
          }
        }
      }

      // 提取时长信息，增加毫秒精度
      if (!duration) {
        const durationMatch = line.match(
          /Duration: (\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/
        );
        if (durationMatch) {
          const [, hours, minutes, seconds, milliseconds = "0"] = durationMatch;
          duration =
            parseInt(hours) * 3600 +
            parseInt(minutes) * 60 +
            parseInt(seconds) +
            parseInt(milliseconds.padEnd(3, "0")) / 1000;

          // 更准确地估算总帧数
          const fpsMatch = line.match(/(\d+(?:\.\d+)?) fps/);
          const fps = fpsMatch ? parseFloat(fpsMatch[1]) : 30;
          totalFrames = Math.round(duration * fps);
          logger.debug(
            `精确检测到视频时长: ${duration}s, 估计帧率: ${fps}, 总帧数: ${totalFrames}`
          );
        }
      }

      // 提取进度信息，增强解析精度
      const timeMatch = line.match(/time=(\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/);
      const fpsMatch = line.match(/fps=\s*(\d+(?:\.\d+)?)/);
      const speedMatch = line.match(/speed=\s*(\d+\.?\d*x)/);
      const frameMatch = line.match(/frame=\s*(\d+)/);

      if (timeMatch || fpsMatch || speedMatch || frameMatch) {
        const currentInfo = this.progressMap.get(taskId);
        if (!currentInfo) return;

        // 检查任务是否已取消
        if (currentInfo.status === "cancelled") {
          wasCancelled = true;
          return;
        }

        // 基本信息
        const updatedInfo: ProgressInfo = {
          ...currentInfo,
          status: "processing",
          stage: currentInfo.stage || "rendering",
          lastUpdate: Date.now(),
        };

        // 计算基于时间的进度
        if (timeMatch) {
          const [, hours, minutes, seconds, milliseconds = "0"] = timeMatch;
          const currentTime =
            parseInt(hours) * 3600 +
            parseInt(minutes) * 60 +
            parseInt(seconds) +
            parseInt(milliseconds.padEnd(3, "0")) / 1000;

          secondsPassed = currentTime;

          if (duration) {
            // FFmpeg处理占总任务的80%（命令生成占20%）
            const progressPercent = (currentTime / duration) * 100;
            // 限制最大进度为95%
            const rawProgress = Math.min(95, progressPercent);
            // 将进度映射到20%-98%区间（命令生成0-20%，渲染20-98%，最终处理98-100%）
            const scaledProgress = 20 + rawProgress * 0.78;
            updatedInfo.progress = Math.floor(scaledProgress);

            // 根据进度更新阶段
            for (let i = renderingStages.length - 1; i >= 0; i--) {
              if (progressPercent >= renderingStages[i].threshold) {
                updatedInfo.stage = renderingStages[i].name;
                break;
              }
            }

            // 防止小进度频繁更新
            if (Math.abs(updatedInfo.progress - lastTimeProgress) >= 1) {
              lastTimeProgress = updatedInfo.progress;
            }
          }
        }

        // 基于帧的进度计算
        if (frameMatch) {
          const [, frame] = frameMatch;
          const currentFrame = parseInt(frame);
          updatedInfo.currentFrame = currentFrame;
          updatedInfo.totalFrames = totalFrames;

          // 计算帧率变化，用于更精确的预测
          const now = Date.now();
          const elapsedMs = now - lastProgressUpdate;

          if (elapsedMs > 2000) {
            // 每2秒计算一次进度
            framesSinceLastProgressUpdate = 0;
            lastProgressUpdate = now;
          }
          framesSinceLastProgressUpdate++;

          // 使用帧数作为进度补充或备用
          if (totalFrames && (!timeMatch || !duration)) {
            // 基于帧的进度上限也为95%
            const frameProgress = Math.min(
              95,
              (currentFrame / totalFrames) * 100
            );
            const scaledProgress = 20 + frameProgress * 0.78;
            updatedInfo.progress = Math.floor(scaledProgress);
          }
        }

        // FPS信息
        if (fpsMatch) {
          const [, fps] = fpsMatch;
          updatedInfo.fps = parseFloat(fps);

          // 如果有fps但没有总帧数，重新计算
          if (!totalFrames && duration) {
            totalFrames = Math.round(duration * parseFloat(fps));
            updatedInfo.totalFrames = totalFrames;
          }
        }

        // 处理速度信息
        if (speedMatch) {
          const [, speed] = speedMatch;
          updatedInfo.speed = parseFloat(speed);

          // 使用速度信息优化进度估计
          // 如果处理速度很慢(<0.5x)，则适当降低显示进度，让用户心理预期更合理
          if (
            updatedInfo.speed &&
            updatedInfo.speed < 0.5 &&
            updatedInfo.progress > 50
          ) {
            updatedInfo.progress = Math.max(50, updatedInfo.progress - 5);
          }
        }

        this.progressMap.set(taskId, updatedInfo);

        // 如果时间超过10秒，至少记录一次调试信息
        if (
          secondsPassed > 10 ||
          Math.abs(updatedInfo.progress - lastTimeProgress) >= 5
        ) {
          logger.debug(
            `任务 ${taskId} 进度更新: ${updatedInfo.progress}%, 阶段: ${updatedInfo.stage}`
          );
        }
      }
    });

    // 专门处理ffmpeg的尾部处理阶段，识别更多的结束标识
    let finalizationStage = 0; // 追踪多阶段结束过程
    let endProcessingTimeout: NodeJS.Timeout | null = null;
    let finalizationStartTime = 0;

    // 识别ffmpeg结束阶段特有的日志信息
    ffmpeg.stderr?.on("data", (data: Buffer) => {
      const line = data.toString();

      // 这些是ffmpeg结束过程中不同阶段的迹象，按照通常出现顺序排列
      // 第1阶段: 编码完成
      if (
        finalizationStage === 0 &&
        line.includes("frame=") &&
        line.includes("time=") &&
        line.includes("fps=") &&
        (line.includes("L q=") || line.includes("speed="))
      ) {
        const timeMatch = line.match(
          /time=(\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/
        );
        if (timeMatch && duration) {
          const [, hours, minutes, seconds] = timeMatch;
          const currentTime =
            parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds);
          // 如果进度已经超过95%的总时长，认为是最终编码阶段
          const progressPercent = (currentTime / duration) * 100;
          if (progressPercent >= 98) {
            finalizationStage = 1;
            finalizationStartTime = Date.now();

            const currentInfo = this.progressMap.get(taskId);
            if (currentInfo && currentInfo.status === "processing") {
              this.progressMap.set(taskId, {
                ...currentInfo,
                progress: 96,
                stage: "finalizing",
                lastUpdate: Date.now(),
              });
              logger.debug(`任务 ${taskId} 已进入最终处理阶段 (96%)`);
            }
          }
        }
      }
      // 第2阶段：写入容器头信息
      else if (
        (finalizationStage === 0 || finalizationStage === 1) &&
        (line.includes("Writing stream header") ||
          line.includes("Starting second pass") ||
          line.includes("Qavg:") ||
          line.includes("video:"))
      ) {
        finalizationStage = 2;
        const currentInfo = this.progressMap.get(taskId);
        if (currentInfo && currentInfo.status === "processing") {
          this.progressMap.set(taskId, {
            ...currentInfo,
            progress: 98,
            stage: "写入文件",
            lastUpdate: Date.now(),
          });
          logger.info(`任务 ${taskId} 正在写入文件头 (98%)`);
        }
      }
      // 第3阶段：合并和封装
      else if (
        (finalizationStage === 1 || finalizationStage === 2) &&
        (line.includes("muxing overhead") ||
          line.includes("video:") ||
          line.includes("audio:") ||
          line.includes("global headers") ||
          line.includes("Writing trailer"))
      ) {
        finalizationStage = 3;
        // 更新为99%，表示封装阶段
        const currentInfo = this.progressMap.get(taskId);
        if (currentInfo && currentInfo.status === "processing") {
          this.progressMap.set(taskId, {
            ...currentInfo,
            progress: 99,
            stage: "封装完成",
            lastUpdate: Date.now(),
          });
          logger.info(`任务 ${taskId} 正在完成封装 (99%)`);

          // 设置超时保障机制，避免卡在99%
          const now = Date.now();
          const finalizationElapsed = now - finalizationStartTime;

          // 如果已经进入最终阶段超过15秒，设置较短的超时
          // 否则设置较长的超时（有些大文件可能需要更久）
          const timeoutDuration = finalizationElapsed > 15000 ? 5000 : 15000;

          // 清理之前的超时
          if (endProcessingTimeout) {
            clearTimeout(endProcessingTimeout);
          }

          // 设置新超时
          endProcessingTimeout = setTimeout(() => {
            const info = this.progressMap.get(taskId);
            if (info && info.status === "processing" && info.progress >= 99) {
              // 超时，强制设为完成
              this.progressMap.set(taskId, {
                ...info,
                status: "completed",
                progress: 100,
                stage: "已完成",
                lastUpdate: Date.now(),
              });
              logger.warn(`任务 ${taskId} 完成超时，强制更新到100%`);
            }
          }, timeoutDuration);
        }
      }
    });

    ffmpeg.on("close", (code: number) => {
      // 清理结束阶段超时
      if (endProcessingTimeout) {
        clearTimeout(endProcessingTimeout);
      }

      // 检查是否手动取消
      const currentStatus = this.progressMap.get(taskId)?.status;
      if (currentStatus === "cancelled" || wasCancelled) {
        // 任务已被标记为取消，保持状态
        logger.info(`任务 ${taskId} 已被手动取消`);
        return;
      }

      if (code === 0) {
        // 成功完成时的信息更丰富
        const elapsedTime = (Date.now() - lastProgressUpdate) / 1000;
        const completionMsg = `任务 ${taskId} 成功完成 (耗时: ${elapsedTime.toFixed(
          1
        )}秒)`;
        logger.info(completionMsg);

        this.progressMap.set(taskId, {
          status: "completed",
          progress: 100,
          currentFrame: totalFrames,
          totalFrames,
          fps: null,
          speed: null,
          stage: "已完成",
          lastUpdate: Date.now(),
        });
      } else {
        // 失败时的错误处理
        logger.error(`任务 ${taskId} 失败，退出代码: ${code}`);
        this.progressMap.set(taskId, {
          status: "failed",
          progress: 0,
          currentFrame: null,
          totalFrames: null,
          fps: null,
          speed: null,
          stage: "处理失败",
          error: `进程退出，代码 ${code}`,
          lastUpdate: Date.now(),
        });
      }
    });
  }

  public cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.progressMap.clear();
    logger.info("Progress tracker cleaned up");
  }
}
